'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { useChat } from '@ai-sdk/react'
import { DefaultChatTransport } from 'ai'
import type { UIMessage } from 'ai'
import { useTabStore } from '../stores/useTabStore'

// Types
type ConversationData = {
  id: string
  title?: string
  createdAt: string
  updatedAt: string
  metadata?: any
}

type UseAiConversationV2Options = {
  // Existing conversation ID to load
  conversationId?: string
  // API endpoint for chat (should be /api/aipane/chat-v2)
  apiEndpoint: string
  // Model and settings
  model?: string
  context?: string
  settings?: any
  // Context for new conversations
  contextEntityType?: string
  contextEntityId?: string
  contextIds?: string[]
  // Tab ID for title updates
  tabId?: string
  // Enable/disable the hook (useful for preventing race conditions)
  enabled?: boolean
}

type UseAiConversationV2Return = {
  // Conversation data
  conversation: ConversationData | null
  isLoadingConversation: boolean
  conversationError: string | null

  // Messages (AI SDK v5 UIMessage format with parts)
  messages: UIMessage[]

  // Chat functionality (from useChat)
  input: string
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void
  handleSubmit: (e: React.FormEvent) => void
  isLoading: boolean
  append: (message: { role: 'user' | 'assistant'; content: string }) => void
  sendMessage: (options: { text: string }) => void
  status: 'idle' | 'streaming' | 'error' | 'submitted' | 'ready'
  error: Error | null | undefined

  // Utilities
  refreshConversation: () => Promise<void>
}

export function useAiConversationV2(
  options: UseAiConversationV2Options
): UseAiConversationV2Return {
  const {
    conversationId: initialConversationId,
    apiEndpoint,
    model = 'gpt-4.1',
    context,
    settings = {},
    contextEntityType,
    contextEntityId,
    contextIds,
    tabId,
    enabled = true,
  } = options

  const { updateTabAiPaneData } = useTabStore()

  // Conversation state
  const [conversation, setConversation] = useState<ConversationData | null>(
    null
  )
  const [isLoadingConversation, setIsLoadingConversation] =
    useState<boolean>(false)
  const [conversationError, setConversationError] = useState<string | null>(
    null
  )

  // Refs for cleanup
  const isMountedRef = useRef(true)
  const abortControllerRef = useRef<AbortController | null>(null)

  // Generate a stable chat key for useChat
  const chatKey = initialConversationId || 'new-conversation'

  // Only initialize useChat when enabled and conversationId is available
  const shouldInitializeChat = enabled && Boolean(chatKey)

  const chat = useChat(
    !shouldInitializeChat
      ? undefined // Do not mount the hook until ready
      : {
          id: chatKey,
          transport: new DefaultChatTransport({
            api: apiEndpoint,
            body: {
              model,
              context,
              settings,
              conversationId: initialConversationId,
              contextEntityType,
              contextEntityId,
              contextIds,
            },
          }),
          // AI SDK v5 performance optimizations
          experimental_throttle: 200,
          onFinish: () => {
            console.log('✅ [useAiConversationV2] Stream finished')

            // Refresh persisted conversation after streaming
            if (initialConversationId) {
              setTimeout(() => {
                refreshConversation().catch(() => {})
              }, 120)
            }
          },
          onError: error => {
            console.error('❌ [useAiConversationV2] Chat error:', error)
          },
        }
  )

  const { messages, sendMessage, status, error } = chat || {
    messages: [] as UIMessage[],
    sendMessage: () => {},
    status: 'idle' as const,
    error: null,
  }

  // Simple input state management
  const [input, setInput] = useState('')

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (input.trim()) {
      sendMessage({ text: input })
      setInput('')
    }
  }

  const append = (message: { role: 'user' | 'assistant'; content: string }) => {
    sendMessage({ text: message.content })
  }

  const isLoading = status === 'streaming'

  // Load conversation data
  const refreshConversation = useCallback(async () => {
    if (!initialConversationId || !isMountedRef.current) {
      return
    }

    setIsLoadingConversation(true)
    setConversationError(null)

    try {
      // Abort any existing request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      abortControllerRef.current = new AbortController()

      const response = await fetch(
        `/api/aipane/conversations/${initialConversationId}`,
        {
          signal: abortControllerRef.current.signal,
        }
      )

      if (!response.ok) {
        throw new Error(`Failed to load conversation: ${response.status}`)
      }

      const data = await response.json()

      if (!isMountedRef.current) {
        return
      }

      setConversation(data.conversation)

      // Update tab title if available
      if (tabId && data.conversation?.title) {
        updateTabAiPaneData(tabId, {
          conversationId: data.conversation.id,
        })
      }
    } catch (error: any) {
      if (error.name === 'AbortError') {
        return
      }

      console.error(
        '❌ [useAiConversationV2] Failed to load conversation:',
        error
      )

      if (isMountedRef.current) {
        setConversationError(error.message || 'Failed to load conversation')
      }
    } finally {
      if (isMountedRef.current) {
        setIsLoadingConversation(false)
      }
    }
  }, [initialConversationId, tabId, updateTabAiPaneData])

  // Load conversation on mount
  useEffect(() => {
    if (initialConversationId && enabled) {
      refreshConversation()
    }
  }, [initialConversationId, enabled, refreshConversation])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  return {
    // Conversation data
    conversation,
    isLoadingConversation,
    conversationError,

    // Messages (AI SDK v5 format with parts)
    messages,

    // Chat functionality
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    append,
    sendMessage,
    status,
    error,

    // Utilities
    refreshConversation,
  }
}
