'use client'

import React, { useRef } from 'react'
import { FiMessageCircle, FiSettings, FiCpu } from 'react-icons/fi'
import { Button } from '@/components/ui/button'
import toast from 'react-hot-toast'
import { useTabStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'
import { generateAiConversationId } from '@/lib/id'
import { getAccessPermissions } from '@/app/configs/tier-permissions'
import { useSession } from 'next-auth/react'
import { SubscriptionTier } from '@prisma/client'
import { useAiConversationV2 } from '../../hooks/useAiConversationV2'
import ChatV2MessageList from '../chat/ChatV2MessageList'
import ChatV2Input from '../chat/ChatV2Input'
import type { Tab } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'

type ChatTabContentV2Props = {
  tab: Tab
  dragTreeId: string
}

export default function ChatTabContentV2({
  tab,
  dragTreeId,
}: ChatTabContentV2Props) {
  const { data: session } = useSession()
  const { updateTabAiPaneData } = useTabStore()

  // Generate conversation ID once and store it
  const conversationIdRef = useRef<string>(generateAiConversationId())

  // Get user permissions
  const userTier =
    (session?.user as any)?.subscriptionTier || SubscriptionTier.FREE
  const permissions = getAccessPermissions(userTier)

  // API endpoint
  const apiEndpoint = '/api/aipane/chat-v2'

  // Use conversation ID from tab store or the generated one
  const conversationId =
    tab.aiPaneData?.conversationId || conversationIdRef.current
  const isConversationReady = Boolean(
    conversationId && conversationId.startsWith('thread_')
  )

  // Context content function
  const contextContent = () => {
    try {
      const contextIds = tab.aiPaneData?.contextIds || []
      if (contextIds.length === 0) return ''

      // Simple context placeholder for now
      return `Context from ${contextIds.length} documents`
    } catch (error) {
      console.error('Error getting context:', error)
      return ''
    }
  }

  // Use the new conversation v2 hook
  const conversation = useAiConversationV2({
    conversationId: isConversationReady ? conversationId : undefined,
    apiEndpoint,
    model: tab.aiPaneData?.model || 'gpt-4.1',
    context: contextContent(),
    settings: tab.aiPaneData?.settings || {},
    contextEntityType: 'drag_tree',
    contextEntityId: dragTreeId,
    contextIds: tab.aiPaneData?.contextIds || [],
    tabId: tab.id,
    // Disable the hook until conversation is ready
    enabled: isConversationReady,
  })

  // Check if user can use chat
  if (!permissions.canCreateAiChat) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <FiMessageCircle className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <h2 className="text-lg font-medium text-gray-500 mb-2">
            Chat Not Available
          </h2>
          <p className="text-sm text-gray-400">
            Upgrade your plan to access AI chat functionality
          </p>
        </div>
      </div>
    )
  }

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    sendMessage,
    status,
    conversation: conversationData,
    isLoadingConversation,
  } = conversation

  // Compute context count from tab store or conversation metadata
  let _meta: any = (conversationData as any)?.metadata
  if (typeof _meta === 'string') {
    try {
      _meta = JSON.parse(_meta)
    } catch {
      _meta = undefined
    }
  }
  const contextCount =
    tab.aiPaneData?.contextIds?.length ?? _meta?.contextIds?.length ?? 0

  // Handle start chat
  const handleStartChat = async () => {
    try {
      // Ensure conversation ID is set in tab store
      if (!tab.aiPaneData?.conversationId) {
        updateTabAiPaneData(tab.id, {
          conversationId: conversationIdRef.current,
          model: 'gpt-4.1',
          contextIds: tab.aiPaneData?.contextIds || [],
        })
      }

      // Send initial message
      sendMessage({
        text: "Hello! I'm ready to help you with your questions. What would you like to know?",
      })

      toast.success('Chat started!')
    } catch (error) {
      console.error('Failed to start chat:', error)
      toast.error('Failed to start chat')
    }
  }

  // Handle custom submit to ensure conversation ID is set
  const handleCustomSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!input.trim()) return

    // Ensure conversation ID is set in tab store
    if (!tab.aiPaneData?.conversationId) {
      updateTabAiPaneData(tab.id, {
        conversationId: conversationIdRef.current,
        model: tab.aiPaneData?.model || 'gpt-4.1',
        contextIds: tab.aiPaneData?.contextIds || [],
      })
    }

    // Submit the message
    handleSubmit(e)
  }

  // Show loading state while conversation is being loaded
  if (isLoadingConversation) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="flex flex-col items-center gap-2 text-gray-500 animate-pulse">
          <FiMessageCircle className="w-8 h-8" />
          <span className="text-sm">Loading conversation...</span>
        </div>
      </div>
    )
  }

  // Show empty state if no messages
  if (messages.length === 0) {
    return (
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <FiMessageCircle className="w-5 h-5 text-blue-600" />
            <div>
              <h2 className="font-medium text-gray-900">AI Chat v2</h2>
              <p className="text-xs text-gray-500">
                {contextCount > 0
                  ? `Using ${contextCount} context document${contextCount === 1 ? '' : 's'}`
                  : 'No context documents selected'}
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="text-gray-500 hover:text-gray-700"
          >
            <FiSettings className="w-4 h-4" />
          </Button>
        </div>

        {/* Empty state */}
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md">
            <FiCpu className="w-16 h-16 mx-auto mb-6 text-gray-300" />
            <h3 className="text-xl font-medium text-gray-900 mb-3">
              Start a Conversation
            </h3>
            <p className="text-gray-500 mb-6">
              Chat with AI to get help with your questions. The AI has access to
              web search and can provide comprehensive answers.
            </p>
            <Button
              onClick={handleStartChat}
              disabled={status === 'streaming'}
              className="px-6 py-3"
            >
              {status === 'streaming' ? 'Starting...' : 'Start Chat'}
            </Button>
            <p className="text-xs text-gray-400 mt-4">
              This is Chat v2 with proper tool call support
            </p>
          </div>
        </div>
      </div>
    )
  }

  // Show chat interface
  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <FiMessageCircle className="w-5 h-5 text-blue-600" />
          <div>
            <h2 className="font-medium text-gray-900">AI Chat v2</h2>
            <p className="text-xs text-gray-500">
              {contextCount > 0
                ? `Using ${contextCount} context document${contextCount === 1 ? '' : 's'}`
                : 'No context documents selected'}
            </p>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          className="text-gray-500 hover:text-gray-700"
        >
          <FiSettings className="w-4 h-4" />
        </Button>
      </div>

      {/* Messages */}
      <ChatV2MessageList
        messages={messages}
        isLoading={isLoading}
        className="flex-1 min-h-0"
      />

      {/* Input */}
      <ChatV2Input
        input={input}
        onInputChange={handleInputChange}
        onSubmit={handleCustomSubmit}
        disabled={!permissions.canCreateAiMessage}
        isLoading={isLoading}
        placeholder="Message AI Assistant..."
      />
    </div>
  )
}
