'use client'

import React, { useEffect, useRef } from 'react'
import { Fi<PERSON><PERSON>, FiArrowDown } from 'react-icons/fi'
import type { UIMessage } from 'ai'
import {
  Conversation,
  ConversationContent,
  ConversationScrollButton,
} from '@/components/ai-elements/conversation'
import { Message, MessageContent } from '@/components/ai-elements/message'
import { Response } from '@/components/ai-elements/response'
import {
  Tool,
  ToolHeader,
  ToolContent,
  ToolInput,
  ToolOutput,
} from '@/components/ai-elements/tool'
import {
  Reasoning,
  ReasoningTrigger,
  ReasoningContent,
} from '@/components/ai-elements/reasoning'

type ChatV2MessageListProps = {
  messages: UIMessage[]
  isLoading?: boolean
  className?: string
}

export default function ChatV2MessageList({
  messages,
  isLoading = false,
  className = '',
}: ChatV2MessageListProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }, [messages.length])

  // Filter out system messages for display
  const displayMessages = messages.filter(message => message.role !== 'system')

  if (displayMessages.length === 0) {
    return (
      <div
        className={`flex items-center justify-center h-full text-gray-500 ${className}`}
      >
        <div className="text-center">
          <FiCpu className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <p className="text-lg font-medium">Start a conversation</p>
          <p className="text-sm">Send a message to begin chatting with AI</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      <Conversation className="flex-1">
        <ConversationContent>
          {displayMessages.map((message, messageIndex) => (
            <Message key={message.id} from={message.role}>
              <MessageContent>
                {/* Render message parts */}
                {message.parts?.map((part, partIndex) => {
                  // Handle text parts
                  if (part.type === 'text') {
                    return (
                      <Response
                        key={`${message.id}-text-${partIndex}`}
                        defaultOrigin="https://localhost:3000"
                      >
                        {part.text}
                      </Response>
                    )
                  }

                  // Handle reasoning parts (for o1-series models)
                  if (part.type === 'reasoning') {
                    return (
                      <Reasoning
                        key={`${message.id}-reasoning-${partIndex}`}
                        isStreaming={
                          isLoading &&
                          messageIndex === displayMessages.length - 1
                        }
                        defaultOpen={false}
                      >
                        <ReasoningTrigger />
                        <ReasoningContent>{part.text}</ReasoningContent>
                      </Reasoning>
                    )
                  }

                  // Handle tool UI parts
                  const maybeTool = part as any
                  if (
                    maybeTool &&
                    typeof maybeTool === 'object' &&
                    'state' in maybeTool &&
                    'type' in maybeTool &&
                    (('input' in maybeTool && maybeTool.input !== undefined) ||
                      ('output' in maybeTool &&
                        maybeTool.output !== undefined) ||
                      ('errorText' in maybeTool && maybeTool.errorText))
                  ) {
                    return (
                      <Tool key={`${message.id}-tool-${partIndex}`}>
                        <ToolHeader
                          type={maybeTool.type}
                          state={maybeTool.state}
                        />
                        <ToolContent>
                          {'input' in maybeTool && maybeTool.input && (
                            <ToolInput input={maybeTool.input} />
                          )}
                          {'output' in maybeTool && (
                            <ToolOutput
                              output={maybeTool.output}
                              errorText={maybeTool.errorText}
                            />
                          )}
                        </ToolContent>
                      </Tool>
                    )
                  }

                  // Handle text-delta parts (streaming text)
                  if ((part as any).type === 'text-delta') {
                    return (
                      <Response
                        key={`${message.id}-text-delta-${partIndex}`}
                        defaultOrigin="https://localhost:3000"
                      >
                        {(part as any).textDelta}
                      </Response>
                    )
                  }

                  // Handle unknown part types gracefully
                  console.warn('Unknown message part type:', part.type, part)
                  return null
                })}

                {/* Show loading indicator for streaming messages */}
                {isLoading && messageIndex === displayMessages.length - 1 && (
                  <div className="flex items-center gap-2 text-xs text-gray-500 mt-2">
                    <div className="flex space-x-1">
                      <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" />
                      <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce delay-100" />
                      <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce delay-200" />
                    </div>
                    <span>AI is thinking...</span>
                  </div>
                )}
              </MessageContent>
            </Message>
          ))}

          {/* Scroll anchor */}
          <div ref={messagesEndRef} />
        </ConversationContent>

        {/* Scroll to bottom button */}
        <ConversationScrollButton>
          <FiArrowDown className="w-4 h-4" />
        </ConversationScrollButton>
      </Conversation>
    </div>
  )
}
