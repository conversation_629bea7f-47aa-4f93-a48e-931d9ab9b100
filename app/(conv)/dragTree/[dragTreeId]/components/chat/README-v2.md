# Chat v2 Components - AI SDK v5 Compatible

This directory contains the Chat v2 implementation that properly handles AI SDK v5 message format with tool calls and streaming.

## Key Improvements over v1

### 1. **Proper Tool Call Display**
- Uses AI SDK Elements (`Tool`, `Tool<PERSON>eader`, `ToolContent`, `ToolInput`, `ToolOutput`)
- Tool calls appear as separate parts in message structure
- No more empty messages when tools are used

### 2. **AI SDK v5 Native Support**
- Uses `UIMessage` format with `parts` array
- Supports `text`, `text-delta`, `reasoning`, and tool UI parts
- Compatible with `useChat` hook from `@ai-sdk/react`

### 3. **Enhanced Message Rendering**
- Uses AI SDK Elements for consistent UI
- Proper streaming indicators
- Auto-scroll functionality with scroll-to-bottom button

## Components

### ChatTabContentV2.tsx
**Main chat interface component**

- Manages conversation lifecycle using `useAiConversationV2`
- Handles permissions and user tier validation
- Provides empty state and start chat functionality
- Integrates with tab store for conversation persistence

**Key Features:**
- Permission-based access control
- Context document integration
- Conversation ID management
- Real-time streaming support

### ChatV2MessageList.tsx
**Message rendering component using AI SDK Elements**

- Uses `Conversation`, `ConversationContent`, `ConversationScrollButton`
- Renders `Message` and `MessageContent` with proper styling
- Handles all message part types:
  - `text` - Regular text content using `Response` component
  - `reasoning` - Model reasoning using `Reasoning` component
  - Tool UI parts - Tool calls/results using `Tool` component
  - `text-delta` - Streaming text updates

**Key Features:**
- Auto-scroll to bottom on new messages
- Loading indicators for streaming
- Graceful handling of unknown part types
- System message filtering

### ChatV2Input.tsx
**Input component using AI SDK Elements**

- Uses `PromptInput`, `PromptInputTextarea`, `PromptInputSubmit`
- Character limit validation
- Keyboard shortcuts (Enter to send, Shift+Enter for new line)
- Loading state management

**Key Features:**
- Input validation with visual feedback
- Character counter
- Disabled state handling
- Consistent styling with AI SDK Elements

## Usage

### Basic Integration

```typescript
import ChatTabContentV2 from './ChatTabContentV2'

// Replace existing ChatTabContent with v2
<ChatTabContentV2 
  tab={tab} 
  dragTreeId={dragTreeId} 
/>
```

### Message Structure

Chat v2 uses AI SDK v5 `UIMessage` format:

```typescript
{
  id: 'msg_123',
  role: 'assistant',
  parts: [
    {
      type: 'text',
      text: 'I\'ll help you with that. Let me search for information.'
    },
    {
      type: 'tool-call',
      toolCallId: 'call_123',
      toolName: 'web_search',
      args: { query: 'search term' },
      state: 'in-progress'
    },
    {
      type: 'tool-result',
      toolCallId: 'call_123',
      result: { /* search results */ },
      state: 'complete'
    },
    {
      type: 'text',
      text: 'Based on my search, here\'s what I found...'
    }
  ]
}
```

### Tool Call Rendering

Tool calls are automatically rendered using AI SDK Elements:

```typescript
// Automatic rendering in ChatV2MessageList
{message.parts.map((part, index) => {
  if (part.type === 'tool-call' || part.type === 'tool-result') {
    return (
      <Tool key={`${message.id}-tool-${index}`}>
        <ToolHeader type={part.type} state={part.state} />
        <ToolContent>
          {part.input && <ToolInput input={part.input} />}
          {part.output && <ToolOutput output={part.output} errorText={part.errorText} />}
        </ToolContent>
      </Tool>
    )
  }
})}
```

## API Integration

Chat v2 components work with the `/api/aipane/chat-v2` endpoint:

```typescript
// useAiConversationV2 hook configuration
const conversation = useAiConversationV2({
  conversationId: 'thread_abc123',
  apiEndpoint: '/api/aipane/chat-v2', // v2 endpoint
  model: 'gpt-4.1',
  context: contextContent(),
  contextIds: ['doc_1', 'doc_2'],
  enabled: true,
})
```

## Migration from v1

### Component Replacement
```typescript
// ❌ Old v1 components
import ChatTabContent from './ChatTabContent'
import SimpleMessageList from './SimpleMessageList'
import SimpleChatInput from './SimpleChatInput'

// ✅ New v2 components
import ChatTabContentV2 from './ChatTabContentV2'
import ChatV2MessageList from './ChatV2MessageList'
import ChatV2Input from './ChatV2Input'
```

### Hook Replacement
```typescript
// ❌ Old v1 hook
import { useAiConversation } from '../hooks/useAiConversation'

// ✅ New v2 hook
import { useAiConversationV2 } from '../hooks/useAiConversationV2'
```

### API Endpoint Update
```typescript
// ❌ Old v1 endpoint
apiEndpoint: '/api/aipane/chat'

// ✅ New v2 endpoint
apiEndpoint: '/api/aipane/chat-v2'
```

## Benefits

1. **Tool Calls Work**: No more empty messages when AI uses tools
2. **Better UX**: Proper loading states and streaming indicators
3. **Consistent UI**: Uses AI SDK Elements for standardized components
4. **Future-Proof**: Built for AI SDK v5 with proper message structure
5. **Maintainable**: Cleaner code structure with better separation of concerns

## Testing

To test tool call functionality:
1. Start a chat in the v2 interface
2. Ask questions that require web search: "What's the weather today?"
3. Verify tool calls appear as separate UI elements
4. Confirm final AI response incorporates tool results
5. Check that streaming works smoothly without interruption
