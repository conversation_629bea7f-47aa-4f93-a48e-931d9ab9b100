// Types for AI Pane Chat v2 API - AI SDK v5 Compatible

import type { UIMessage } from 'ai'

/**
 * Chat v2 request payload - uses AI SDK v5 UIMessage format
 */
export type ChatV2Request = {
  /** Messages array in AI SDK v5 UIMessage format with parts */
  messages: UIMessage[]
  /** Model to use (defaults to gpt-4.1) */
  model?: string
  /** Context content for the conversation */
  context?: string
  /** Additional settings for the AI model */
  settings?: Record<string, any>
  /** Conversation ID for persistence (must start with 'thread_') */
  conversationId?: string
  /** Context entity type (e.g., 'drag_tree') */
  contextEntityType?: string
  /** Context entity ID */
  contextEntityId?: string
  /** Context document IDs to include */
  contextIds?: string[]
}

/**
 * Chat v2 response - streams UIMessage format with proper tool call support
 */
export type ChatV2Response = {
  /** Streamed UIMessage with parts array containing text, tool calls, reasoning */
  message: UIMessage
  /** Optional metadata about the response */
  metadata?: {
    /** Search results if web search was used */
    searchMetadata?: {
      citations: any[]
      searchQueries: string[]
    }
    /** Token usage information */
    usage?: {
      promptTokens: number
      completionTokens: number
      totalTokens: number
    }
    /** Model used for the response */
    model: string
  }
}
