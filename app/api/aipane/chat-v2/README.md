# AI Pane Chat v2 Endpoint

**Path:** `/api/aipane/chat-v2`

A production-grade streaming chat endpoint built for AI SDK v5 compatibility, properly handling tool calls and message parts structure.

## Key Improvements over v1

- **AI SDK v5 Native**: Uses `UIMessage` format with `parts` array for proper tool call handling
- **Tool Call Display**: Tool calls are properly rendered in the message parts structure
- **Streaming Reliability**: Uses `convertToModelMessages()` and proper message format conversion
- **Multi-step Tool Calling**: Supports complex tool calling sequences with `stopWhen(stepCountIs(10))`

## Request Format

```typescript
POST /api/aipane/chat-v2
Content-Type: application/json

{
  "messages": [
    {
      "id": "msg_123",
      "role": "user",
      "parts": [
        {
          "type": "text",
          "text": "What's the weather in San Francisco?"
        }
      ]
    }
  ],
  "model": "gpt-4.1",
  "conversationId": "thread_abc123",
  "contextIds": ["doc_1", "doc_2"]
}
```

## Response Format

Returns an AI SDK v5 `UIMessageStreamResponse` with proper tool call parts:

```
Content-Type: text/event-stream

data: {"type":"text","text":"I'll help you check the weather in San Francisco."}

data: {"type":"tool-call","toolCallId":"call_123","toolName":"web_search","args":{"query":"San Francisco weather today"}}

data: {"type":"tool-result","toolCallId":"call_123","result":{"temperature":"72°F","conditions":"Sunny"}}

data: {"type":"text","text":"The current weather in San Francisco is 72°F and sunny."}

data: [DONE]
```

## Key Features

### 1. Proper Tool Call Handling
- Tool calls appear as separate parts in the message structure
- Frontend can render tool calls using AI SDK Elements `Tool` component
- No more empty messages when tools are used

### 2. AI SDK v5 Compatibility
- Uses `convertToModelMessages()` for proper format conversion
- Supports `UIMessage` format with parts array
- Compatible with `useChat` hook from `@ai-sdk/react`

### 3. Multi-step Tool Calling
- Uses `stopWhen(stepCountIs(10))` for complex tool sequences
- Ensures AI provides final response after tool usage
- Prevents incomplete tool calling scenarios

### 4. Persistence & Metadata
- Saves conversations with full `UIMessage` metadata
- Tracks execution steps and tool usage
- Logs AI usage for billing and analytics

## Usage with Frontend

```typescript
import { useChat } from '@ai-sdk/react'
import { DefaultChatTransport } from 'ai'

const chat = useChat({
  transport: new DefaultChatTransport({
    api: '/api/aipane/chat-v2',
    body: {
      model: 'gpt-4.1',
      conversationId: 'thread_abc123',
      contextIds: ['doc_1', 'doc_2'],
    },
  }),
})

// Messages will have proper parts structure for tool calls
chat.messages.forEach(message => {
  message.parts.forEach(part => {
    if (part.type === 'text') {
      // Render text content
    } else if (part.type === 'tool-call') {
      // Render tool call UI
    } else if (part.type === 'tool-result') {
      // Render tool result UI
    }
  })
})
```

## Rendering Tool Calls

Use AI SDK Elements for proper tool call rendering:

```typescript
import { Tool, ToolHeader, ToolContent, ToolInput, ToolOutput } from '@/components/ai-elements/tool'

// In your message rendering component
{message.parts.map((part, index) => {
  if (part.type === 'tool-call' || part.type === 'tool-result') {
    return (
      <Tool key={`${message.id}-tool-${index}`}>
        <ToolHeader type={part.type} state={part.state} />
        <ToolContent>
          {part.input && <ToolInput input={part.input} />}
          {part.output && <ToolOutput output={part.output} errorText={part.errorText} />}
        </ToolContent>
      </Tool>
    )
  }
  return null
})}
```

## Migration from v1

The main difference is message format:

```typescript
// ❌ v1 Format (causes empty messages with tool calls)
{
  role: 'user',
  content: 'What is the weather?'
}

// ✅ v2 Format (proper tool call support)
{
  id: 'msg_123',
  role: 'user',
  parts: [
    {
      type: 'text',
      text: 'What is the weather?'
    }
  ]
}
```

## Error Handling

- **401**: Unauthorized (no valid session)
- **429**: Rate limit exceeded
- **400**: Invalid request format or conversation ID
- **500**: Internal server error

All errors return plain text responses for simplicity.
