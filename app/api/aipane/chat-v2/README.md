# AI Pane Chat v2 Endpoint

**Path:** `/api/aipane/chat-v2`

A production-grade streaming chat endpoint with improved architecture that handles message history retrieval internally.

## Key Improvements over v1

- **Server-Side History Management**: API retrieves conversation history from database internally
- **Reduced Payload Size**: Frontend sends only new user message, not entire conversation history
- **Context as System Message**: Context is properly handled as system message server-side
- **AI SDK v5 Native**: Proper tool call handling with streaming support
- **Better Separation of Concerns**: Frontend focuses on UI, backend handles conversation logic

## Request Format

```typescript
POST /api/aipane/chat-v2
Content-Type: application/json

// First message (includes context)
{
  "message": "What's the weather in San Francisco?",
  "conversationId": "thread_abc123",
  "context": "User is asking about weather. Provide current conditions.",
  "model": "gpt-4.1",
  "contextIds": ["doc_1", "doc_2"],
  "dragTreeId": "tree_123"
}

// Subsequent messages (no context needed)
{
  "message": "What about tomorrow's forecast?",
  "conversationId": "thread_abc123",
  "model": "gpt-4.1"
}
```

## Response Format

Returns an AI SDK v5 `UIMessageStreamResponse` with proper tool call parts:

```
Content-Type: text/event-stream

data: {"type":"text","text":"I'll help you check the weather in San Francisco."}

data: {"type":"tool-call","toolCallId":"call_123","toolName":"web_search","args":{"query":"San Francisco weather today"}}

data: {"type":"tool-result","toolCallId":"call_123","result":{"temperature":"72°F","conditions":"Sunny"}}

data: {"type":"text","text":"The current weather in San Francisco is 72°F and sunny."}

data: [DONE]
```

## Key Features

### 1. Proper Tool Call Handling

- Tool calls appear as separate parts in the message structure
- Frontend can render tool calls using AI SDK Elements `Tool` component
- No more empty messages when tools are used

### 2. AI SDK v5 Compatibility

- Uses `convertToModelMessages()` for proper format conversion
- Supports `UIMessage` format with parts array
- Compatible with `useChat` hook from `@ai-sdk/react`

### 3. Multi-step Tool Calling

- Uses `stopWhen(stepCountIs(10))` for complex tool sequences
- Ensures AI provides final response after tool usage
- Prevents incomplete tool calling scenarios

### 4. Persistence & Metadata

- Saves conversations with full `UIMessage` metadata
- Tracks execution steps and tool usage
- Logs AI usage for billing and analytics

## Usage with Frontend

```typescript
import { useChat } from '@ai-sdk/react'
import { DefaultChatTransport } from 'ai'

const chat = useChat({
  transport: new DefaultChatTransport({
    api: '/api/aipane/chat-v2',
    body: {
      model: 'gpt-4.1',
      conversationId: 'thread_abc123',
      contextIds: ['doc_1', 'doc_2'],
    },
  }),
})

// Messages will have proper parts structure for tool calls
chat.messages.forEach(message => {
  message.parts.forEach(part => {
    if (part.type === 'text') {
      // Render text content
    } else if (part.type === 'tool-call') {
      // Render tool call UI
    } else if (part.type === 'tool-result') {
      // Render tool result UI
    }
  })
})
```

## Rendering Tool Calls

Use AI SDK Elements for proper tool call rendering:

```typescript
import { Tool, ToolHeader, ToolContent, ToolInput, ToolOutput } from '@/components/ai-elements/tool'

// In your message rendering component
{message.parts.map((part, index) => {
  if (part.type === 'tool-call' || part.type === 'tool-result') {
    return (
      <Tool key={`${message.id}-tool-${index}`}>
        <ToolHeader type={part.type} state={part.state} />
        <ToolContent>
          {part.input && <ToolInput input={part.input} />}
          {part.output && <ToolOutput output={part.output} errorText={part.errorText} />}
        </ToolContent>
      </Tool>
    )
  }
  return null
})}
```

## Migration from v1

The main difference is message format:

```typescript
// ❌ v1 Format (causes empty messages with tool calls)
{
  role: 'user',
  content: 'What is the weather?'
}

// ✅ v2 Format (proper tool call support)
{
  id: 'msg_123',
  role: 'user',
  parts: [
    {
      type: 'text',
      text: 'What is the weather?'
    }
  ]
}
```

## Error Handling

- **401**: Unauthorized (no valid session)
- **429**: Rate limit exceeded
- **400**: Invalid request format or conversation ID
- **500**: Internal server error

All errors return plain text responses for simplicity.
