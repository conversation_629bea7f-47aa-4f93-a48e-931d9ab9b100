import { streamText, stepCountIs } from 'ai'
import { azure } from '@ai-sdk/azure'
import { auth } from '@/auth'
import { NextRequest } from 'next/server'
import { ExecutionStepCollector } from '@/app/server-actions/ai-chat'
import { buildSearchTools } from '@/app/api/dragtree/shared/search-tools'
import prisma from '@/app/libs/prismadb'
import { ENABLE_CHAT_DEBUG_LOGGING } from '@/app/configs/feature-flags'

// Model configuration
const MODEL_CONFIG = {
  TEMPERATURE: 0.7,
  MAX_TOKENS: 2000,
}

// Types for the improved API
type ChatV2Request = {
  message: string // Only the new user message
  conversationId: string
  context?: string // Only included on first message or when context changes
  model?: string
  contextIds?: string[]
  dragTreeId?: string
}

/**
 * Chat v2 API Route - Improved Architecture
 *
 * Key improvements:
 * - API handles message history retrieval internally
 * - Frontend sends only new user message
 * - Context is handled as system message server-side
 * - Reduced payload size for long conversations
 * - Better separation of concerns
 */
export async function POST(request: NextRequest) {
  try {
    // Authentication
    const session = await auth()
    if (!session?.user?.id) {
      return new Response('Unauthorized', { status: 401 })
    }

    // Parse request body
    const body: ChatV2Request = await request.json()
    const {
      message,
      conversationId,
      context,
      model = 'gpt-4.1',
      contextIds = [],
      dragTreeId,
    } = body

    if (ENABLE_CHAT_DEBUG_LOGGING) {
      console.log('🚀 [Chat v2] Processing request:', {
        messageLength: message?.length,
        model,
        conversationId,
        hasContext: !!context,
        contextIds: contextIds?.length,
      })
    }

    // Validate required fields
    if (!message || typeof message !== 'string') {
      return new Response('Message is required', { status: 400 })
    }

    if (!conversationId || !conversationId.startsWith('thread_')) {
      return new Response('Valid conversation ID is required', { status: 400 })
    }

    // Retrieve conversation history from database
    const existingMessages = await prisma.aiMessage.findMany({
      where: { conversationId },
      select: {
        role: true,
        content: true,
        createdAt: true,
      },
      orderBy: { createdAt: 'asc' },
    })

    if (ENABLE_CHAT_DEBUG_LOGGING) {
      console.log(
        `📚 [Chat v2] Retrieved ${existingMessages.length} existing messages`
      )
    }

    // Build message array for AI model
    const modelMessages: Array<{
      role: 'system' | 'user' | 'assistant'
      content: string
    }> = []

    // Add system message with context (only if context is provided)
    if (context && context.trim()) {
      modelMessages.push({
        role: 'system',
        content: `You are a helpful AI assistant with access to web search tools. Use the following context to help answer questions:

${context}

When using tools, always provide a comprehensive response that incorporates the tool results.
IMPORTANT: After using any tools, you MUST provide a final response that summarizes the results and answers the user's question.`,
      })
    } else {
      // Default system message without context
      modelMessages.push({
        role: 'system',
        content: `You are a helpful AI assistant with access to web search tools.
When using tools, always provide a comprehensive response that incorporates the tool results.
IMPORTANT: After using any tools, you MUST provide a final response that summarizes the results and answers the user's question.`,
      })
    }

    // Add existing conversation history (excluding system messages)
    existingMessages
      .filter(msg => msg.role !== 'SYSTEM')
      .forEach(msg => {
        modelMessages.push({
          role: msg.role.toLowerCase() as 'user' | 'assistant',
          content: msg.content,
        })
      })

    // Add the new user message
    modelMessages.push({
      role: 'user',
      content: message,
    })

    if (ENABLE_CHAT_DEBUG_LOGGING) {
      console.log(
        `💬 [Chat v2] Built ${modelMessages.length} messages for AI model`
      )
    }

    // Set up execution step collector for persistence
    const stepCollector = new ExecutionStepCollector()

    // Build search tools
    const tools = buildSearchTools([], () => {})

    // Stream the response using AI SDK v5
    const result = streamText({
      model: azure(model),
      messages: modelMessages,
      tools,
      toolChoice: 'auto',
      temperature: MODEL_CONFIG.TEMPERATURE,
      maxOutputTokens: MODEL_CONFIG.MAX_TOKENS,
      // Enable multi-step tool calling
      stopWhen: stepCountIs(10),

      // Track tool calls for persistence
      onChunk: async chunk => {
        const kind = (chunk as any)?.chunk?.type as string | undefined
        if (kind === 'tool-call') {
          stepCollector.addStep({
            providerType: kind,
            metadata: { raw: (chunk as any).chunk },
          })
        }
        if (kind === 'tool-result') {
          stepCollector.addStep({
            providerType: kind,
            metadata: { raw: (chunk as any).chunk },
          })
        }
      },

      // Handle completion for persistence
      onFinish: async ({ text, usage }) => {
        if (ENABLE_CHAT_DEBUG_LOGGING) {
          console.log('✅ [Chat v2] Stream finished', { usage })
        }

        try {
          // Persist the new user message and assistant response
          await prisma.$transaction([
            // Save user message
            prisma.aiMessage.create({
              data: {
                conversationId,
                role: 'USER',
                content: message,
              },
            }),
            // Save assistant message
            prisma.aiMessage.create({
              data: {
                conversationId,
                role: 'ASSISTANT',
                content: text || '',
                metadata: {
                  model,
                  usage,
                  contextIds,
                  toolsUsed: stepCollector.getSteps().length > 0,
                },
              },
            }),
          ])

          if (ENABLE_CHAT_DEBUG_LOGGING) {
            console.log('💾 [Chat v2] Messages persisted successfully')
          }
        } catch (error) {
          console.error('❌ [Chat v2] Persistence error:', error)
          // Don't fail the response if persistence fails
        }
      },
    })

    // Return the UI message stream response
    return result.toUIMessageStreamResponse()
  } catch (error) {
    console.error('❌ [Chat v2] API error:', error)
    return new Response('Internal server error', { status: 500 })
  }
}
