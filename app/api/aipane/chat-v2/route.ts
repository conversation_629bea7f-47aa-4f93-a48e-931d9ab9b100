import { streamText, convertToModelMessages, stepCountIs } from 'ai'
import { azure } from '@ai-sdk/azure'
import { auth } from '@/auth'
import { NextRequest } from 'next/server'
import { ExecutionStepCollector } from '@/app/server-actions/ai-chat'
import { buildSearchTools } from '@/app/api/dragtree/shared/search-tools'

// Model configuration
const MODEL_CONFIG = {
  TEMPERATURE: 0.7,
  MAX_TOKENS: 2000,
}

/**
 * Chat v2 API Route - AI SDK v5 Compatible
 *
 * This route properly handles AI SDK v5 message format with parts array
 * and tool calls, based on working chat-demo patterns.
 */
export async function POST(request: NextRequest) {
  try {
    // Authentication
    const session = await auth()
    if (!session?.user?.id) {
      return new Response('Unauthorized', { status: 401 })
    }

    // Skip rate limiting for now in v2

    // Parse request body
    const body = await request.json()
    const {
      messages,
      model = 'gpt-4.1',
      conversationId,
      contextIds = [],
    } = body

    console.log('🚀 [Chat v2] Processing request:', {
      messageCount: messages?.length,
      model,
      conversationId,
      contextIds: contextIds?.length,
    })

    // Validate messages array
    if (!Array.isArray(messages) || messages.length === 0) {
      return new Response('Messages array is required', { status: 400 })
    }

    // Validate conversation ID format
    if (conversationId && !conversationId.startsWith('thread_')) {
      return new Response('Invalid conversation ID format', { status: 400 })
    }

    // Convert UIMessage format to ModelMessage format for AI SDK
    const modelMessages = convertToModelMessages(messages)
    console.log('🔄 [Chat v2] Converted messages:', {
      original: messages.length,
      converted: modelMessages.length,
    })

    // Set up execution step collector for persistence
    const stepCollector = new ExecutionStepCollector()

    // Build search tools (simplified)
    const tools = buildSearchTools([], () => {})

    // Stream the response using AI SDK v5
    const result = streamText({
      model: azure(model),
      messages: modelMessages,
      tools,
      toolChoice: 'auto',
      temperature: MODEL_CONFIG.TEMPERATURE,
      maxOutputTokens: MODEL_CONFIG.MAX_TOKENS,
      // Enable multi-step tool calling - this is key for proper tool execution
      stopWhen: stepCountIs(10),
      // System prompt to ensure proper tool usage
      system: `You are a helpful AI assistant with access to web search tools.
When using tools, always provide a comprehensive response that incorporates the tool results.
IMPORTANT: After using any tools, you MUST provide a final response that summarizes the results and answers the user's question.`,

      // Track tool calls for persistence
      onChunk: async chunk => {
        const kind = (chunk as any)?.chunk?.type as string | undefined
        if (kind === 'tool-call') {
          stepCollector.addStep({
            providerType: kind,
            metadata: { raw: (chunk as any).chunk },
          })
        }
        if (kind === 'tool-result') {
          stepCollector.addStep({
            providerType: kind,
            metadata: { raw: (chunk as any).chunk },
          })
        }
      },

      // Handle completion for persistence
      onFinish: async ({ usage }) => {
        console.log('✅ [Chat v2] Stream finished', { usage })
        // Skip persistence for now in v2
      },
    })

    // Return the UI message stream response
    return result.toUIMessageStreamResponse()
  } catch (error) {
    console.error('❌ [Chat v2] API error:', error)
    return new Response('Internal server error', { status: 500 })
  }
}
